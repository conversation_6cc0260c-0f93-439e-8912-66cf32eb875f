[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "hybridpipe"
version = "2.0.0"
description = "A unified messaging interface for microservices and distributed systems"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Anand S", email = "<EMAIL>"}
]
maintainers = [
    {name = "Anand S", email = "<EMAIL>"}
]
keywords = ["messaging", "microservices", "kafka", "nats", "mqtt", "rabbitmq", "redis", "zeromq"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: System :: Networking",
    "Topic :: System :: Distributed Computing",
]
requires-python = ">=3.12"
dependencies = [
    # Core dependencies
    "asyncio-mqtt>=0.16.0",
    "aioredis>=2.0.1",
    "msgpack>=1.0.7",
    "protobuf>=4.25.0",
    "pydantic>=2.5.0",
    "structlog>=23.2.0",
    
    # Protocol implementations
    "confluent-kafka>=2.3.0",
    "aio-pika>=9.3.0",
    "paho-mqtt>=1.6.1",
    "nats-py>=2.6.0",
    "pyzmq>=25.1.0",
    "redis>=5.0.0",
    
    # Monitoring and metrics
    "prometheus-client>=0.19.0",
    "opentelemetry-api>=1.21.0",
    "opentelemetry-sdk>=1.21.0",
    "opentelemetry-instrumentation>=0.42b0",
    
    # Utilities
    "click>=8.1.7",
    "rich>=13.7.0",
    "uvloop>=0.19.0; sys_platform != 'win32'",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "mypy>=1.7.0",
    "ruff>=0.1.6",
    "pre-commit>=3.5.0",
]
docs = [
    "sphinx>=7.2.0",
    "sphinx-rtd-theme>=1.3.0",
    "sphinx-autodoc-typehints>=1.25.0",
    "myst-parser>=2.0.0",
]
examples = [
    "jupyter>=1.0.0",
    "matplotlib>=3.8.0",
    "pandas>=2.1.0",
]

[project.urls]
Homepage = "https://github.com/AnandSGit/hybridpipe.io"
Documentation = "https://hybridpipe.readthedocs.io"
Repository = "https://github.com/AnandSGit/hybridpipe.io"
Issues = "https://github.com/AnandSGit/hybridpipe.io/issues"

[project.scripts]
hybridpipe = "hybridpipe.cli:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["hybridpipe*"]
exclude = ["tests*", "docs*", "examples*"]

[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["hybridpipe"]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
asyncio_mode = "auto"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["hybridpipe"]
omit = [
    "*/tests/*",
    "*/examples/*",
    "*/docs/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.ruff]
target-version = "py312"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"tests/**/*" = ["B011"]

[package]
name = "hybridpipe"
version = "0.1.0"
edition = "2021"
authors = ["Anand S <<EMAIL>>"]
description = "A unified messaging interface for microservices and distributed systems"
license = "MIT"
repository = "https://github.com/AnandSGit/hybridpipe.io"
readme = "README.md"
keywords = ["messaging", "microservices", "kafka", "nats", "mqtt"]
categories = ["network-programming", "asynchronous"]

[lib]
name = "hybridpipe"
path = "src/lib.rs"

[[bin]]
name = "hybridpipe"
path = "src/main.rs"

[dependencies]
# Core dependencies
tokio = { version = "1.36", features = ["full"] }
async-trait = "0.1"
thiserror = "1.0"
anyhow = "1.0"
log = "0.4"
env_logger = "0.11"
config = "0.14"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"
bytes = "1.5"
futures = "0.3"
uuid = { version = "1.7", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
tracing = "0.1"
tracing-subscriber = "0.3"
dashmap = "5.5"
lazy_static = "1.4"

# Serialization
rmp-serde = "1.1"  # MessagePack
prost = "0.12"     # Protocol Buffers
bincode = "1.3"    # Binary serialization (similar to Gob)
flate2 = "1.0"     # Compression

# Kafka - temporarily commented out due to build issues
# rdkafka = { version = "0.36", features = ["dynamic-linking"] }

# NATS
async-nats = "0.33"

# MQTT
rumqttc = "0.24"

# RabbitMQ and AMQP
lapin = { version = "2.3" }

# Redis
redis = { version = "0.24", features = ["tokio-comp"] }

# TCP
tokio-util = { version = "0.7", features = ["codec"] }

# ZeroMQ
zmq = "0.10"

# NSQ
# Temporarily removed due to compatibility issues with stable Rust
# nsq-client = "0.1"

# Testing
mockall = "0.12"
tokio-test = "0.4"

[dev-dependencies]
criterion = "0.5"
tempfile = "3.10"
test-log = "0.2"
proptest = "1.4"

[[bench]]
name = "serialization_benchmark"
harness = false

[[bench]]
name = "protocol_benchmark"
harness = false

version: '3.8'

services:
  # Redis for testing Redis protocol
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5

  # <PERSON><PERSON><PERSON> and Zookeeper for testing Kafka protocol
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    healthcheck:
      test: ["CMD", "bash", "-c", "echo 'ruok' | nc localhost 2181"]
      interval: 10s
      timeout: 5s
      retries: 5

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    depends_on:
      zookeeper:
        condition: service_healthy
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092,PLAINTEXT_HOST://kafka:29092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 10s
      timeout: 5s
      retries: 5

  # RabbitMQ for testing RabbitMQ protocol
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    ports:
      - "5672:5672"
      - "15672:15672"  # Management UI
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Eclipse Mosquitto for testing MQTT protocol
  mosquitto:
    image: eclipse-mosquitto:2.0
    ports:
      - "1883:1883"
      - "9001:9001"  # WebSocket
    volumes:
      - ./docker/mosquitto.conf:/mosquitto/config/mosquitto.conf
    healthcheck:
      test: ["CMD", "mosquitto_pub", "-h", "localhost", "-t", "test", "-m", "health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # NATS for testing NATS protocol (Phase 3)
  nats:
    image: nats:2.10-alpine
    ports:
      - "4222:4222"
      - "8222:8222"  # HTTP monitoring
      - "6222:6222"  # Cluster port
    command: [
      "--jetstream",
      "--http_port", "8222",
      "--store_dir", "/data",
      "--max_file_store", "1GB",
      "--max_mem_store", "256MB"
    ]
    volumes:
      - nats_data:/data
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8222/healthz"]
      interval: 10s
      timeout: 5s
      retries: 5

  # QPID Broker for testing AMQP 1.0 protocol (Phase 3)
  qpid-broker:
    image: scholzj/qpid-cpp:1.39.0
    ports:
      - "5673:5672"  # Use different port to avoid conflict with RabbitMQ
    environment:
      QPIDD_ADMIN_USERNAME: admin
      QPIDD_ADMIN_PASSWORD: admin
    healthcheck:
      test: ["CMD", "qpid-stat", "-b", "localhost:5672"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Test runner service
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile.test
    depends_on:
      redis:
        condition: service_healthy
      kafka:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      mosquitto:
        condition: service_healthy
      nats:
        condition: service_healthy
      qpid-broker:
        condition: service_healthy
    environment:
      - REDIS_HOST=redis
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - RABBITMQ_HOST=rabbitmq
      - MQTT_HOST=mosquitto
      - NATS_HOST=nats
      - AMQP1_HOST=qpid-broker
      - AMQP1_PORT=5672
    volumes:
      - .:/app
    working_dir: /app
    command: ["python", "-m", "pytest", "tests/", "-v", "--tb=short"]

volumes:
  nats_data:
    driver: local

networks:
  default:
    name: hybridpipe-test
